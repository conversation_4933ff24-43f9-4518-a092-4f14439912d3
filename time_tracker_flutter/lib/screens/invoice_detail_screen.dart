import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'package:time_tracker_flutter/services/client_service.dart';
import 'package:time_tracker_flutter/services/invoice_service.dart';
import 'package:time_tracker_flutter/services/pdf_service.dart';
import 'package:time_tracker_flutter/services/invoice_localization_service.dart';
import 'package:time_tracker_flutter/services/currency_service.dart';
import 'package:time_tracker_flutter/screens/invoice_create/invoice_create_screen.dart';
import 'package:time_tracker_flutter/widgets/confirm_dialog.dart';
import 'package:time_tracker_flutter/widgets/common_loading_indicator.dart';
import 'package:time_tracker_flutter/utils/ui_helpers.dart';
import 'package:time_tracker_flutter/utils/locale_date_utils.dart';
import 'package:time_tracker_flutter/screens/invoice_settings_screen.dart';
import 'package:time_tracker_flutter/screens/pdf_preview_screen.dart';
import 'package:time_tracker_flutter/models/invoice_models.dart';

/// Screen for viewing and managing individual invoice details
class InvoiceDetailScreen extends StatefulWidget {
  final String invoiceId;
  final DatabaseService? databaseService;
  final ClientService? clientService;
  final InvoiceService? invoiceService;
  final PDFService? pdfService;

  const InvoiceDetailScreen({
    super.key,
    required this.invoiceId,
    this.databaseService,
    this.clientService,
    this.invoiceService,
    this.pdfService,
  });

  @override
  State<InvoiceDetailScreen> createState() => _InvoiceDetailScreenState();
}

class _InvoiceDetailScreenState extends State<InvoiceDetailScreen> {
  late final DatabaseService _databaseService;
  late final ClientService _clientService;
  late final InvoiceService _invoiceService;
  late final PDFService _pdfService;

  Invoice? _invoice;
  Client? _client;
  List<TimeEntry> _timeEntries = [];
  bool _isLoading = true;
  bool _isGeneratingPdf = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _databaseService = widget.databaseService ?? DatabaseService();
    _clientService = widget.clientService ?? ClientService();
    _invoiceService = widget.invoiceService ?? InvoiceService();
    _pdfService = widget.pdfService ?? PDFService();
    _loadInvoiceData();
  }

  Future<void> _loadInvoiceData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final invoice = await _databaseService.getInvoice(widget.invoiceId);
      if (invoice == null) {
        throw Exception('Invoice not found');
      }

      final client = await _clientService.getClient(invoice.clientId);
      if (client == null) {
        throw Exception('Client not found');
      }

      // Load time entries if they exist
      final timeEntries = <TimeEntry>[];
      for (final timeEntryId in invoice.timeEntryIds) {
        final timeEntry = await _databaseService.getTimeEntry(timeEntryId);
        if (timeEntry != null) {
          timeEntries.add(timeEntry);
        }
      }

      setState(() {
        _invoice = invoice;
        _client = client;
        _timeEntries = timeEntries;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = e.toString();
      });
    }
  }

  Future<void> _updateInvoiceStatus(InvoiceStatus newStatus) async {
    if (_invoice == null) return;

    try {
      await _invoiceService.updateInvoiceStatus(_invoice!.id, newStatus);
      await _loadInvoiceData();

      if (mounted) {
        showSuccessSnackBar('Invoice status updated to ${_getStatusDisplayText(newStatus)}', context);
      }
    } catch (e) {
      if (mounted) {
        showErrorSnackBar('Error updating status: ${e.toString()}', context);
      }
    }
  }

  Future<void> _deleteInvoice() async {
    if (_invoice == null) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => ConfirmDialog(
        title: 'Delete Invoice',
        content: 'Are you sure you want to delete invoice ${_invoice!.invoiceNumber}? This action cannot be undone.',
        confirmText: 'Delete',
        destructive: true,
      ),
    );

    if (confirmed == true) {
      try {
        await _databaseService.deleteInvoice(_invoice!.id);

        if (mounted) {
          showSuccessSnackBar('Invoice deleted successfully', context);
          Navigator.of(context).pop(true); // Return true to indicate deletion
        }
      } catch (e) {
        if (mounted) {
          showErrorSnackBar('Error deleting invoice: ${e.toString()}', context);
        }
      }
    }
  }

  Future<void> _generateAndSharePdf() async {
    if (_invoice == null || _client == null) return;

    setState(() {
      _isGeneratingPdf = true;
    });

    try {
      // Create basic business info (this would normally come from settings)
      final businessInfo = BusinessInfo(
        name: 'Your Business Name',
        email: '<EMAIL>',
        phone: '+****************',
        address: Address(
          street: '123 Business St',
          city: 'Business City',
          postalCode: '12345',
          country: 'Country',
        ),
      );

      await _pdfService.shareInvoicePDF(
        invoice: _invoice!,
        client: _client!,
        businessInfo: businessInfo,
        template: InvoiceTemplate.professional,
        timeEntries: _timeEntries.isNotEmpty ? _timeEntries : null,
        locale: InvoiceLocalizationService.getLocaleFromLanguage(_invoice!.locale),
      );

      if (mounted) {
        showSuccessSnackBar('PDF generated and shared successfully', context);
      }
    } catch (e) {
      if (mounted) {
        showErrorSnackBar('Error generating PDF: ${e.toString()}', context);
      }
    } finally {
      setState(() {
        _isGeneratingPdf = false;
      });
    }
  }

  Future<void> _previewPdf() async {
    if (_invoice == null || _client == null) return;

    setState(() {
      _isGeneratingPdf = true;
    });

    try {
      // Create basic business info (this would normally come from settings)
      final businessInfo = BusinessInfo(
        name: 'Your Business Name',
        email: '<EMAIL>',
        phone: '+****************',
        address: Address(
          street: '123 Business St',
          city: 'Business City',
          postalCode: '12345',
          country: 'Country',
        ),
      );

      final filePath = await _pdfService.generatePDFForPreview(
        invoice: _invoice!,
        client: _client!,
        businessInfo: businessInfo,
        template: InvoiceTemplate.professional,
        timeEntries: _timeEntries.isNotEmpty ? _timeEntries : null,
        locale: InvoiceLocalizationService.getLocaleFromLanguage(_invoice!.locale),
      );

      if (mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PDFPreviewScreen(
              filePath: filePath,
              title: 'Invoice ${_invoice!.invoiceNumber} Preview',
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        showErrorSnackBar('Error generating PDF preview: ${e.toString()}', context);
      }
    } finally {
      setState(() {
        _isGeneratingPdf = false;
      });
    }
  }

  Future<void> _showStatusChangeDialog() async {
    if (_invoice == null) return;

    final newStatus = await showDialog<InvoiceStatus>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Change Invoice Status'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: InvoiceStatus.values.map((status) {
            return RadioListTile<InvoiceStatus>(
              title: Text(_getStatusDisplayText(status)),
              subtitle: Text(_getStatusDescription(status)),
              value: status,
              groupValue: _invoice!.status,
              onChanged: (value) => Navigator.of(context).pop(value),
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );

    if (newStatus != null && newStatus != _invoice!.status) {
      await _updateInvoiceStatus(newStatus);
    }
  }

  void _editInvoice() {
    if (_invoice == null) return;

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => InvoiceCreateScreen(invoice: _invoice),
      ),
    ).then((result) {
      // Refresh the invoice details if it was updated
      if (result != null) {
        _loadInvoiceData();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Invoice Details'),
        ),
        body: const Center(
          child: CommonLoadingIndicator.large(message: 'Loading invoice...'),
        ),
      );
    }

    if (_errorMessage != null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Invoice Details'),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Theme.of(context).colorScheme.error,
              ),
              const SizedBox(height: 16),
              Text(
                _errorMessage!.contains('Invoice not found')
                    ? 'Invoice not found'
                    : _errorMessage!.contains('Client not found')
                        ? 'Invoice not found'
                        : 'Error loading invoice',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _errorMessage = null;
                  });
                  _loadInvoiceData();
                },
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    if (_invoice == null || _client == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Invoice Details'),
        ),
        body: const Center(
          child: Text('Invoice not found'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text('Invoice ${_invoice!.invoiceNumber}'),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: _editInvoice,
            tooltip: 'Edit Invoice',
          ),
          PopupMenuButton<String>(
            onSelected: (value) async {
              switch (value) {
                case 'status':
                  await _showStatusChangeDialog();
                  break;
                case 'preview':
                  await _previewPdf();
                  break;
                case 'pdf':
                  await _generateAndSharePdf();
                  break;
                case 'delete':
                  await _deleteInvoice();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'status',
                child: ListTile(
                  leading: Icon(Icons.update),
                  title: Text('Change Status'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'preview',
                child: ListTile(
                  leading: Icon(Icons.visibility),
                  title: Text('Preview PDF'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'pdf',
                child: ListTile(
                  leading: Icon(Icons.picture_as_pdf),
                  title: Text('Generate & Share PDF'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: ListTile(
                  leading: Icon(Icons.delete, color: Colors.red),
                  title: Text('Delete Invoice', style: TextStyle(color: Colors.red)),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadInvoiceData,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildInvoiceHeader(),
              const SizedBox(height: 24),
              _buildClientSection(),
              const SizedBox(height: 24),
              _buildInvoiceDetailsSection(),
              const SizedBox(height: 24),
              _buildLineItemsSection(),
              const SizedBox(height: 24),
              _buildTotalsSection(),
              if (_invoice!.notes != null && _invoice!.notes!.isNotEmpty) ...[
                const SizedBox(height: 24),
                _buildNotesSection(),
              ],
              const SizedBox(height: 32),
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInvoiceHeader() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).colorScheme.surface,
            Theme.of(context).colorScheme.surface.withValues(alpha: 0.95),
          ],
        ),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Invoice',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w600,
                      letterSpacing: 0.5,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _invoice!.invoiceNumber,
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      letterSpacing: -0.5,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Icon(
                        Icons.calendar_today_rounded,
                        size: 16,
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                      const SizedBox(width: 6),
                      Text(
                        'Issued: ${LocaleDateUtils.formatDate(_invoice!.issueDate)}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                  if (_invoice!.dueDate != null) ...[
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.schedule_rounded,
                          size: 16,
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                        ),
                        const SizedBox(width: 6),
                        Text(
                          'Due: ${LocaleDateUtils.formatDate(_invoice!.dueDate!)}',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: _invoice!.isOverdue
                                ? Theme.of(context).colorScheme.error
                                : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                _buildStatusChip(_invoice!.status),
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Theme.of(context).colorScheme.primaryContainer,
                        Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.8),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
                    ),
                  ),
                  child: Column(
                    children: [
                      Text(
                        'Total',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onPrimaryContainer.withValues(alpha: 0.8),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        CurrencyService.formatCurrency(
                          _invoice!.total,
                          _invoice!.currency,
                          InvoiceLocalizationService.getLocaleFromLanguage(_invoice!.locale),
                        ),
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onPrimaryContainer,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(InvoiceStatus status) {
    Color backgroundColor;
    Color textColor;
    IconData icon;
    Color borderColor;

    switch (status) {
      case InvoiceStatus.draft:
        backgroundColor = Theme.of(context).colorScheme.surfaceContainerHighest;
        textColor = Theme.of(context).colorScheme.onSurface;
        icon = Icons.edit_note_rounded;
        borderColor = Theme.of(context).colorScheme.outline.withValues(alpha: 0.3);
        break;
      case InvoiceStatus.sent:
        backgroundColor = Theme.of(context).colorScheme.primaryContainer;
        textColor = Theme.of(context).colorScheme.onPrimaryContainer;
        icon = Icons.send_rounded;
        borderColor = Theme.of(context).colorScheme.primary.withValues(alpha: 0.3);
        break;
      case InvoiceStatus.paid:
        backgroundColor = Theme.of(context).colorScheme.tertiaryContainer;
        textColor = Theme.of(context).colorScheme.onTertiaryContainer;
        icon = Icons.check_circle_rounded;
        borderColor = Theme.of(context).colorScheme.tertiary.withValues(alpha: 0.3);
        break;
      case InvoiceStatus.overdue:
        backgroundColor = Theme.of(context).colorScheme.errorContainer;
        textColor = Theme.of(context).colorScheme.onErrorContainer;
        icon = Icons.warning_rounded;
        borderColor = Theme.of(context).colorScheme.error.withValues(alpha: 0.3);
        break;
      case InvoiceStatus.cancelled:
        backgroundColor = Theme.of(context).colorScheme.surfaceContainerHighest;
        textColor = Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6);
        icon = Icons.cancel_rounded;
        borderColor = Theme.of(context).colorScheme.outline.withValues(alpha: 0.3);
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: borderColor,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: textColor.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: textColor,
          ),
          const SizedBox(width: 6),
          Text(
            _getStatusDisplayText(status),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: textColor,
              fontWeight: FontWeight.w600,
              letterSpacing: 0.3,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildClientSection() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).colorScheme.surface,
            Theme.of(context).colorScheme.surface.withValues(alpha: 0.95),
          ],
        ),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Theme.of(context).colorScheme.primary,
                        Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Center(
                    child: Text(
                      _client!.name.isNotEmpty ? _client!.name[0].toUpperCase() : 'C',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Theme.of(context).colorScheme.onPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Bill To',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.w600,
                          letterSpacing: 0.5,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _client!.name,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_client!.email != null) ...[
              _buildClientInfoRow(Icons.email_rounded, _client!.email!),
              const SizedBox(height: 8),
            ],
            if (_client!.phone != null) ...[
              _buildClientInfoRow(Icons.phone_rounded, _client!.phone!),
              const SizedBox(height: 8),
            ],
            if (_client!.address != null) ...[
              _buildClientInfoRow(Icons.location_on_rounded, _client!.address!.formattedAddress),
              const SizedBox(height: 8),
            ],
            if (_client!.taxId != null) ...[
              _buildClientInfoRow(Icons.business_rounded, 'Tax ID: ${_client!.taxId!}'),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildClientInfoRow(IconData icon, String text) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.8),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInvoiceDetailsSection() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).colorScheme.surface,
            Theme.of(context).colorScheme.surface.withValues(alpha: 0.95),
          ],
        ),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline_rounded,
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Invoice Details',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
                ),
              ),
              child: Column(
                children: [
                  _buildModernDetailRow('Currency', _invoice!.currency, Icons.attach_money_rounded),
                  const SizedBox(height: 12),
                  _buildModernDetailRow('Locale', _invoice!.locale, Icons.language_rounded),
                  if (_invoice!.taxRate > 0) ...[
                    const SizedBox(height: 12),
                    _buildModernDetailRow('Tax Rate', '${_invoice!.taxRate.toStringAsFixed(1)}%', Icons.percent_rounded),
                  ],
                  if (_invoice!.servicePeriodStart != null && _invoice!.servicePeriodEnd != null) ...[
                    const SizedBox(height: 12),
                    _buildModernDetailRow(
                      InvoiceLocalizationService.getTranslation('service_period', _invoice!.locale),
                      '${LocaleDateUtils.formatDate(_invoice!.servicePeriodStart!)} - ${LocaleDateUtils.formatDate(_invoice!.servicePeriodEnd!)}',
                      Icons.date_range_rounded,
                    ),
                  ],
                  const SizedBox(height: 12),
                  _buildModernDetailRow('Created', LocaleDateUtils.formatDate(DateTime.parse(_invoice!.createdAt)), Icons.create_rounded),
                  if (_invoice!.updatedAt != null) ...[
                    const SizedBox(height: 12),
                    _buildModernDetailRow('Last Updated', LocaleDateUtils.formatDate(DateTime.parse(_invoice!.updatedAt!)), Icons.update_rounded),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernDetailRow(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Row(
            children: [
              Text(
                '$label:',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  value,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildLineItemsSection() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).colorScheme.surface,
            Theme.of(context).colorScheme.surface.withValues(alpha: 0.95),
          ],
        ),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.receipt_long_rounded,
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Line Items',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_invoice!.additionalItems.isEmpty)
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
                  ),
                ),
                child: Center(
                  child: Column(
                    children: [
                      Icon(
                        Icons.inbox_outlined,
                        size: 48,
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.4),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'No line items',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                        ),
                      ),
                    ],
                  ),
                ),
              )
            else
              ..._invoice!.additionalItems.map((item) => _buildModernLineItem(item)),
          ],
        ),
      ),
    );
  }

  Widget _buildModernLineItem(InvoiceLineItem item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  item.description,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: _getLineItemTypeColor(item.type),
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: _getLineItemTypeColor(item.type).withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Text(
                  _getLineItemTypeText(item.type),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: 10,
                    letterSpacing: 0.5,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: Row(
                  children: [
                    Icon(
                      Icons.shopping_cart_rounded,
                      size: 14,
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Qty: ${_formatQuantity(item.quantity)}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Row(
                  children: [
                    Icon(
                      Icons.attach_money_rounded,
                      size: 14,
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Rate: ${CurrencyService.formatCurrency(
                        item.rate,
                        _invoice!.currency,
                        InvoiceLocalizationService.getLocaleFromLanguage(_invoice!.locale),
                      )}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text(
                'Total: ',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
              Text(
                CurrencyService.formatCurrency(
                  item.amount,
                  _invoice!.currency,
                  InvoiceLocalizationService.getLocaleFromLanguage(_invoice!.locale),
                ),
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTotalsSection() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).colorScheme.surface,
            Theme.of(context).colorScheme.surface.withValues(alpha: 0.95),
          ],
        ),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.calculate_rounded,
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Totals',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
                ),
              ),
              child: Column(
                children: [
                  _buildModernTotalRow(
                    'Subtotal',
                    CurrencyService.formatCurrency(
                      _invoice!.subtotal,
                      _invoice!.currency,
                      InvoiceLocalizationService.getLocaleFromLanguage(_invoice!.locale),
                    ),
                    false,
                  ),
                  if (_invoice!.taxAmount > 0) ...[
                    const SizedBox(height: 8),
                    _buildModernTotalRow(
                      'Tax (${_invoice!.taxRate.toStringAsFixed(1)}%)',
                      CurrencyService.formatCurrency(
                        _invoice!.taxAmount,
                        _invoice!.currency,
                        InvoiceLocalizationService.getLocaleFromLanguage(_invoice!.locale),
                      ),
                      false,
                    ),
                  ],
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Theme.of(context).colorScheme.primaryContainer,
                          Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.8),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
                      ),
                    ),
                    child: _buildModernTotalRow(
                      'Total',
                      CurrencyService.formatCurrency(
                        _invoice!.total,
                        _invoice!.currency,
                        InvoiceLocalizationService.getLocaleFromLanguage(_invoice!.locale),
                      ),
                      true,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernTotalRow(String label, String amount, bool isTotal) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
            fontSize: isTotal ? 16 : 14,
            color: isTotal 
                ? Theme.of(context).colorScheme.onPrimaryContainer
                : Theme.of(context).colorScheme.onSurface,
          ),
        ),
        Text(
          amount,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.bold,
            fontSize: isTotal ? 16 : 14,
            color: isTotal 
                ? Theme.of(context).colorScheme.onPrimaryContainer
                : Theme.of(context).colorScheme.primary,
          ),
        ),
      ],
    );
  }

  Widget _buildNotesSection() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).colorScheme.surface,
            Theme.of(context).colorScheme.surface.withValues(alpha: 0.95),
          ],
        ),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.note_rounded,
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Notes',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
                ),
              ),
              child: Text(
                _invoice!.notes!,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  height: 1.5,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).colorScheme.surface,
            Theme.of(context).colorScheme.surface.withValues(alpha: 0.95),
          ],
        ),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: _buildModernActionButton(
                    context,
                    'Preview PDF',
                    Icons.visibility_rounded,
                    _isGeneratingPdf ? null : _previewPdf,
                    isLoading: _isGeneratingPdf,
                    isPrimary: true,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildModernActionButton(
                    context,
                    'Share PDF',
                    Icons.picture_as_pdf_rounded,
                    _isGeneratingPdf ? null : _generateAndSharePdf,
                    isLoading: _isGeneratingPdf,
                    isPrimary: true,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildModernActionButton(
                    context,
                    'Change Status',
                    Icons.update_rounded,
                    _showStatusChangeDialog,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildModernActionButton(
                    context,
                    'Edit Invoice',
                    Icons.edit_rounded,
                    _editInvoice,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: _buildModernActionButton(
                context,
                'Invoice Settings',
                Icons.settings_rounded,
                () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const InvoiceSettingsScreen(),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernActionButton(
    BuildContext context,
    String label,
    IconData icon,
    VoidCallback? onPressed, {
    bool isPrimary = false,
    bool isLoading = false,
  }) {
    final theme = Theme.of(context);
    
    return Container(
      height: 48,
      decoration: BoxDecoration(
        gradient: isPrimary
            ? LinearGradient(
                colors: [
                  theme.colorScheme.primary,
                  theme.colorScheme.primary.withValues(alpha: 0.8),
                ],
              )
            : null,
        color: isPrimary
            ? null
            : theme.colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isPrimary
              ? theme.colorScheme.primary.withValues(alpha: 0.3)
              : theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
        boxShadow: isPrimary
            ? [
                BoxShadow(
                  color: theme.colorScheme.primary.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ]
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(16),
          child: Center(
            child: isLoading
                ? const CommonLoadingIndicator.small()
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        icon,
                        size: 18,
                        color: isPrimary
                            ? theme.colorScheme.onPrimary
                            : theme.colorScheme.onSurface,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        label,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: isPrimary
                              ? theme.colorScheme.onPrimary
                              : theme.colorScheme.onSurface,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
          ),
        ),
      ),
    );
  }

  String _formatQuantity(double quantity) {
    if (quantity == quantity.roundToDouble()) {
      return quantity.round().toString();
    } else {
      return quantity.toStringAsFixed(2);
    }
  }

  Color _getLineItemTypeColor(InvoiceLineItemType type) {
    switch (type) {
      case InvoiceLineItemType.timeEntry:
        return Colors.blue;
      case InvoiceLineItemType.expense:
        return Colors.orange;
      case InvoiceLineItemType.discount:
        return Colors.green;
      case InvoiceLineItemType.adjustment:
        return Colors.purple;
    }
  }

  String _getLineItemTypeText(InvoiceLineItemType type) {
    switch (type) {
      case InvoiceLineItemType.timeEntry:
        return 'TIME';
      case InvoiceLineItemType.expense:
        return 'EXPENSE';
      case InvoiceLineItemType.discount:
        return 'DISCOUNT';
      case InvoiceLineItemType.adjustment:
        return 'ADJUSTMENT';
    }
  }

  String _getStatusDisplayText(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.draft:
        return 'Draft';
      case InvoiceStatus.sent:
        return 'Sent';
      case InvoiceStatus.paid:
        return 'Paid';
      case InvoiceStatus.overdue:
        return 'Overdue';
      case InvoiceStatus.cancelled:
        return 'Cancelled';
    }
  }

  String _getStatusDescription(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.draft:
        return 'Invoice is being prepared';
      case InvoiceStatus.sent:
        return 'Invoice has been sent to client';
      case InvoiceStatus.paid:
        return 'Payment has been received';
      case InvoiceStatus.overdue:
        return 'Payment is past due date';
      case InvoiceStatus.cancelled:
        return 'Invoice has been cancelled';
    }
  }
}