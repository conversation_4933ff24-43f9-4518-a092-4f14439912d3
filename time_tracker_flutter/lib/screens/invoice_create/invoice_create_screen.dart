import 'package:flutter/material.dart';
import 'package:time_tracker_flutter/models/invoice_models.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/screens/invoice_create/invoice_create_controller.dart';
import 'package:time_tracker_flutter/screens/invoice_create/widgets/invoice_create_app_bar.dart';
import 'package:time_tracker_flutter/screens/invoice_create/widgets/invoice_step_indicator.dart';
import 'package:time_tracker_flutter/screens/invoice_create/widgets/invoice_navigation_buttons.dart';
import 'package:time_tracker_flutter/screens/invoice_create/steps/client_time_entries_step.dart';
import 'package:time_tracker_flutter/screens/invoice_create/steps/rate_configuration_step.dart';
import 'package:time_tracker_flutter/screens/invoice_create/steps/settings_step.dart';
import 'package:time_tracker_flutter/screens/invoice_create/widgets/invoice_preview_tab.dart';
import 'package:time_tracker_flutter/utils/ui_helpers.dart';
import 'package:time_tracker_flutter/widgets/common_loading_indicator.dart';

/// Screen for creating and editing invoices with step-by-step flow
class InvoiceCreateScreen extends StatefulWidget {
  /// Optional invoice to edit (null for creating new invoice)
  final Invoice? invoice;

  /// Optional initial client selection
  final Client? initialClient;

  /// Optional initial time entries selection
  final List<TimeEntry>? initialTimeEntries;

  const InvoiceCreateScreen({
    super.key,
    this.invoice,
    this.initialClient,
    this.initialTimeEntries,
  });

  @override
  State<InvoiceCreateScreen> createState() => _InvoiceCreateScreenState();
}

class _InvoiceCreateScreenState extends State<InvoiceCreateScreen>
    with TickerProviderStateMixin {
  late final InvoiceCreateController _controller;
  final PageController _pageController = PageController();

  @override
  void initState() {
    super.initState();
    _controller = InvoiceCreateController(
      invoice: widget.invoice,
      initialClient: widget.initialClient,
      initialTimeEntries: widget.initialTimeEntries,
      vsync: this,
    );
    _controller.initialize();

    // Listen to controller changes to sync PageController
    _controller.addListener(_syncPageController);
  }

  void _syncPageController() {
    // Sync PageController with current step when not in preview mode
    if (!_controller.showPreview && _pageController.hasClients && mounted) {
      final currentPage = _pageController.page?.round() ?? 0;
      if (currentPage != _controller.currentStep) {
        // Use a post-frame callback to avoid conflicts during build
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted && _pageController.hasClients) {
            _pageController.animateToPage(
              _controller.currentStep,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
          }
        });
      }
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Scaffold(
          appBar: InvoiceCreateAppBar(
            isEditing: widget.invoice != null,
            currentStep: _controller.currentStep,
            showPreview: _controller.showPreview,
            onPreviewToggle: _controller.togglePreview,
          ),
          body: _controller.isLoading
              ? const Center(
                  child: CommonLoadingIndicator.large(
                    message: 'Loading invoice data...',
                  ),
                )
              : Column(
                  children: [
                    InvoiceStepIndicator(
                      currentStep: _controller.currentStep,
                      canProceedFromStep: _controller.canProceedFromStep,
                    ),
                    Expanded(
                      child: _controller.currentStep == 2 && _controller.showPreview
                          ? InvoicePreviewTab(controller: _controller)
                          : PageView(
                              controller: _pageController,
                              physics: const NeverScrollableScrollPhysics(), // Disable swipe navigation
                              children: [
                                ClientTimeEntriesStep(controller: _controller),
                                RateConfigurationStep(controller: _controller),
                                SettingsStep(controller: _controller),
                              ],
                            ),
                    ),
                    InvoiceNavigationButtons(
                      currentStep: _controller.currentStep,
                      canProceedFromStep: _controller.canProceedFromStep,
                      isEditing: widget.invoice != null,
                      onPrevious: _previousStep,
                      onNext: _nextStep,
                      onSave: _saveInvoice,
                    ),
                  ],
                ),
        );
      },
    );
  }

  void _previousStep() {
    if (_controller.currentStep > 0) {
      final newStep = _controller.currentStep - 1;
      _controller.setCurrentStep(newStep);

      // Navigate to the new step
      if (_pageController.hasClients && mounted) {
        _pageController.animateToPage(
          newStep,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    }
  }

  void _nextStep() {
    if (_controller.currentStep < 2) {
      final newStep = _controller.currentStep + 1;
      _controller.setCurrentStep(newStep);

      // Navigate to the new step
      if (_pageController.hasClients && mounted) {
        _pageController.animateToPage(
          newStep,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    }
  }

  Future<void> _saveInvoice() async {
    try {
      await _controller.saveInvoice();

      if (mounted) {
        // Show success message
        showSuccessSnackBar(widget.invoice != null
                ? 'Invoice updated successfully!'
                : 'Invoice created successfully!', context);

        // Navigate back to the invoice list screen
        Navigator.of(context).pop(true); // Return true to indicate successful save
      }
    } catch (e) {
      if (mounted) {
        // Show error message to user
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving invoice: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }
}