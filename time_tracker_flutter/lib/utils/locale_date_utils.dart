import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:time_tracker_flutter/services/database_service.dart';

/// Utility class for region-aware date formatting
class LocaleDateUtils {
  static final DatabaseService _databaseService = DatabaseService();

  // Cache for custom date format settings to enable synchronous access
  static bool? _cachedCustomDateFormatEnabled;
  static String? _cachedCustomDateFormat;
  static DateTime? _cacheLastUpdated;

  /// Update the cache with current custom date format settings
  static Future<void> updateCustomDateFormatCache() async {
    try {
      _cachedCustomDateFormatEnabled = await _databaseService.isCustomDateFormatEnabled();
      _cachedCustomDateFormat = await _databaseService.getCustomDateFormat();
      _cacheLastUpdated = DateTime.now();
    } catch (e) {
      // If there's an error, clear the cache
      _cachedCustomDateFormatEnabled = null;
      _cachedCustomDateFormat = null;
      _cacheLastUpdated = null;
    }
  }

  /// Check if cache is valid (updated within the last 5 minutes)
  static bool _isCacheValid() {
    if (_cacheLastUpdated == null) return false;
    return DateTime.now().difference(_cacheLastUpdated!).inMinutes < 5;
  }

  /// Get custom date format synchronously using cache
  static DateFormat? _getCachedCustomDateFormat([Locale? locale]) {
    if (!_isCacheValid() || _cachedCustomDateFormatEnabled != true || _cachedCustomDateFormat == null) {
      return null;
    }
    try {
      return DateFormat(_cachedCustomDateFormat!, locale?.toString());
    } catch (e) {
      return null;
    }
  }

  /// Get locale-aware date format for general dates (e.g., "Mar 15, 2024" in US, "15 Mar 2024" in UK)
  static DateFormat getDateFormat([Locale? locale]) {
    return DateFormat.yMMMd(locale?.toString());
  }

  /// Get custom or locale-aware date format for general dates
  static Future<DateFormat> getDateFormatWithCustom([Locale? locale]) async {
    final useCustom = await _databaseService.isCustomDateFormatEnabled();
    if (useCustom) {
      final customFormat = await _databaseService.getCustomDateFormat();
      if (customFormat != null && customFormat.isNotEmpty) {
        try {
          return DateFormat(customFormat, locale?.toString());
        } catch (e) {
          // If custom format is invalid, fall back to default
          return getDateFormat(locale);
        }
      }
    }
    return getDateFormat(locale);
  }

  /// Get locale-aware short date format (e.g., "3/15/24" in US, "15/3/24" in UK)
  static DateFormat getShortDateFormat([Locale? locale]) {
    return DateFormat.yMd(locale?.toString());
  }

  /// Get locale-aware time format (24h vs 12h based on locale)
  static DateFormat getTimeFormat([Locale? locale]) {
    return DateFormat.Hm(locale?.toString());
  }

  /// Get locale-aware weekday format
  static DateFormat getWeekdayFormat([Locale? locale]) {
    return DateFormat.E(locale?.toString());
  }

  /// Get locale-aware full weekday format
  static DateFormat getFullWeekdayFormat([Locale? locale]) {
    return DateFormat.EEEE(locale?.toString());
  }

  /// Get locale-aware month format
  static DateFormat getMonthFormat([Locale? locale]) {
    return DateFormat.MMM(locale?.toString());
  }

  /// Get locale-aware full month format
  static DateFormat getFullMonthFormat([Locale? locale]) {
    return DateFormat.MMMM(locale?.toString());
  }

  /// Get locale-aware month-year format
  static DateFormat getMonthYearFormat([Locale? locale]) {
    return DateFormat.yMMM(locale?.toString());
  }

  /// Get locale-aware full month-year format
  static DateFormat getFullMonthYearFormat([Locale? locale]) {
    return DateFormat.yMMMM(locale?.toString());
  }

  /// Format a DateTime to a locale-aware date string with cached custom format support
  static String formatDate(DateTime date, [Locale? locale]) {
    // Try to use cached custom format first
    final customFormat = _getCachedCustomDateFormat(locale);
    if (customFormat != null) {
      try {
        return customFormat.format(date);
      } catch (e) {
        // Fall back to default format if custom format fails
      }
    }
    return getDateFormat(locale).format(date);
  }

  /// Format a DateTime to a locale-aware date string (async version that supports custom formats)
  static Future<String> formatDateAsync(DateTime date, [Locale? locale]) async {
    final format = await getDateFormatWithCustom(locale);
    return format.format(date);
  }

  /// Format a DateTime to a custom or locale-aware date string
  static Future<String> formatDateWithCustom(DateTime date, [Locale? locale]) async {
    final format = await getDateFormatWithCustom(locale);
    return format.format(date);
  }

  /// Format a DateTime to a custom or locale-aware date string with context
  static Future<String> formatDateWithCustomAndContext(DateTime date, BuildContext context) async {
    final locale = await getEffectiveLocale(context);
    return formatDateWithCustom(date, locale);
  }

  /// Format a DateTime with both custom locale and custom format support
  static Future<String> formatDateWithAllCustom(DateTime date, BuildContext context) async {
    final effectiveLocale = await getEffectiveLocale(context);
    final format = await getDateFormatWithCustom(effectiveLocale);
    return format.format(date);
  }

  /// Format a DateTime to a locale-aware short date string
  static String formatShortDate(DateTime date, [Locale? locale]) {
    return getShortDateFormat(locale).format(date);
  }

  /// Format a DateTime to a locale-aware time string
  static String formatTime(DateTime date, [Locale? locale]) {
    return getTimeFormat(locale).format(date);
  }

  /// Format a DateTime to a locale-aware weekday string
  static String formatWeekday(DateTime date, [Locale? locale]) {
    return getWeekdayFormat(locale).format(date);
  }

  /// Format a DateTime to a locale-aware month string
  static String formatMonth(DateTime date, [Locale? locale]) {
    return getMonthFormat(locale).format(date);
  }

  /// Format a DateTime to a locale-aware month-year string
  static String formatMonthYear(DateTime date, [Locale? locale]) {
    return getMonthYearFormat(locale).format(date);
  }

  /// Get the current locale from context
  static Locale getCurrentLocale(BuildContext context) {
    try {
      return Localizations.localeOf(context);
    } catch (e) {
      debugPrint('getCurrentLocale error: $e');
      return const Locale('en');
    }
  }

  /// Get the current locale with custom locale override support
  static Future<Locale> getCurrentLocaleWithCustom(BuildContext context) async {
    final useCustom = await _databaseService.isCustomLocaleEnabled();
    if (useCustom) {
      final customLocaleString = await _databaseService.getCustomLocale();
      if (customLocaleString != null && customLocaleString.isNotEmpty) {
        try {
          final parts = customLocaleString.split('_');
          if (parts.length == 2) {
            return Locale(parts[0], parts[1]);
          } else if (parts.length == 1) {
            return Locale(parts[0]);
          }
        } catch (e) {
          // If custom locale is invalid, fall back to system locale
        }
      }
    }
    return getCurrentLocale(context);
  }

  /// Get effective locale (custom override or system locale)
  static Future<Locale> getEffectiveLocale(BuildContext context) async {
    return getCurrentLocaleWithCustom(context);
  }

  /// Format date range in locale-aware format
  static String formatDateRange(DateTime start, DateTime end, [Locale? locale]) {
    final dateFormat = getDateFormat(locale);

    if (start.year != end.year) {
      // Different years: use full date format for both
      return '${dateFormat.format(start)} - ${dateFormat.format(end)}';
    } else if (start.month != end.month) {
      // Same year, different months: use full date format for both
      return '${dateFormat.format(start)} - ${dateFormat.format(end)}';
    } else {
      // Same month and year: use smart range formatting
      // Check if the locale puts day first by testing a known date
      final testDate = DateTime(2024, 3, 15); // March 15, 2024
      final testFormatted = dateFormat.format(testDate);
      final dayFirst = testFormatted.startsWith('15') || testFormatted.startsWith('15/') || testFormatted.startsWith('15 ');

      if (dayFirst) {
        // Day-first format: "15-20 Mar 2024"
        final monthYear = getMonthFormat(locale).format(start);
        return '${start.day}-${end.day} $monthYear ${start.year}';
      } else {
        // Month-first format: "Mar 15-20, 2024"
        final monthFormat = getMonthFormat(locale);
        return '${monthFormat.format(start)} ${start.day}-${end.day}, ${start.year}';
      }
    }
  }

  /// Format date range with custom format support
  static Future<String> formatDateRangeAsync(DateTime start, DateTime end, [Locale? locale]) async {
    final dateFormat = await getDateFormatWithCustom(locale);

    if (start.year != end.year) {
      // Different years: use full date format for both
      return '${dateFormat.format(start)} - ${dateFormat.format(end)}';
    } else if (start.month != end.month) {
      // Same year, different months: use full date format for both
      return '${dateFormat.format(start)} - ${dateFormat.format(end)}';
    } else {
      // Same month and year: use smart range formatting
      // Check if the locale puts day first by testing a known date
      final testDate = DateTime(2024, 3, 15); // March 15, 2024
      final testFormatted = dateFormat.format(testDate);
      final dayFirst = testFormatted.startsWith('15') || testFormatted.startsWith('15/') || testFormatted.startsWith('15 ');

      if (dayFirst) {
        // Day-first format: "15-20 Mar 2024"
        final monthYear = getMonthFormat(locale).format(start);
        return '${start.day}-${end.day} $monthYear ${start.year}';
      } else {
        // Month-first format: "Mar 15-20, 2024"
        final monthFormat = getMonthFormat(locale);
        return '${monthFormat.format(start)} ${start.day}-${end.day}, ${start.year}';
      }
    }
  }

  /// Format week display in locale-aware format
  static String formatWeekDisplay(DateTime weekStart, DateTime weekEnd, [Locale? locale]) {
    final dateFormat = getDateFormat(locale);

    if (weekStart.month == weekEnd.month && weekStart.year == weekEnd.year) {
      // Same month: use smart range formatting
      // Check if the locale puts day first by testing a known date
      final testDate = DateTime(2024, 3, 15); // March 15, 2024
      final testFormatted = dateFormat.format(testDate);
      final dayFirst = testFormatted.startsWith('15') || testFormatted.startsWith('15/') || testFormatted.startsWith('15 ');

      if (dayFirst) {
        // Day-first format: "1-7 Mar 2024"
        final monthYear = getMonthFormat(locale).format(weekStart);
        return '${weekStart.day}-${weekEnd.day} $monthYear ${weekStart.year}';
      } else {
        // Month-first format: "Mar 1-7, 2024"
        final monthFormat = getMonthFormat(locale);
        return '${monthFormat.format(weekStart)} ${weekStart.day}-${weekEnd.day}, ${weekStart.year}';
      }
    } else if (weekStart.year == weekEnd.year) {
      // Different months, same year: use full date format for both
      return '${dateFormat.format(weekStart)} - ${dateFormat.format(weekEnd)}';
    } else {
      // Different years: use full date format for both
      return '${dateFormat.format(weekStart)} - ${dateFormat.format(weekEnd)}';
    }
  }

  /// Format week display with custom format support
  static Future<String> formatWeekDisplayAsync(DateTime weekStart, DateTime weekEnd, [Locale? locale]) async {
    final dateFormat = await getDateFormatWithCustom(locale);

    if (weekStart.month == weekEnd.month && weekStart.year == weekEnd.year) {
      // Same month: try to create a smart range display
      final startFormatted = dateFormat.format(weekStart);
      final endFormatted = dateFormat.format(weekEnd);

      // For same month, try to create a compact range like "15-21 Mar 2024" or "Mar 15-21, 2024"
      // depending on the date format pattern

      // Check if the format puts day first (like dd/MM/yyyy or dd MMM yyyy)
      final testDate = DateTime(2024, 3, 15); // March 15, 2024
      final testFormatted = dateFormat.format(testDate);
      final dayFirst = testFormatted.startsWith('15') || testFormatted.startsWith('15/') || testFormatted.startsWith('15 ');

      if (dayFirst) {
        // Day-first format: try "15-21 Mar 2024" style
        final monthYear = getMonthFormat(locale).format(weekStart);
        return '${weekStart.day}-${weekEnd.day} $monthYear ${weekStart.year}';
      } else {
        // Month-first format: try "Mar 15-21, 2024" style
        final monthFormat = getMonthFormat(locale);
        return '${monthFormat.format(weekStart)} ${weekStart.day}-${weekEnd.day}, ${weekStart.year}';
      }
    } else {
      // Different months or years: show full dates with custom format
      return '${dateFormat.format(weekStart)} - ${dateFormat.format(weekEnd)}';
    }
  }

  /// Check if the locale uses 24-hour time format
  static bool uses24HourTime(Locale locale) {
    // Common locales that use 24-hour time
    const use24Hour = {
      'de', 'fr', 'it', 'es', 'pt', 'ru', 'ja', 'ko', 'zh', 'ar', 'hi',
      'nl', 'sv', 'no', 'da', 'fi', 'pl', 'cs', 'hu', 'el', 'tr'
    };

    return use24Hour.contains(locale.languageCode);
  }

  /// Get appropriate time format based on locale preference
  static DateFormat getPreferredTimeFormat([Locale? locale]) {
    if (locale != null && uses24HourTime(locale)) {
      return DateFormat.Hm(locale.toString()); // 24-hour format
    } else {
      // For US/UK and other 12-hour locales, let the system decide
      return DateFormat.jm(locale?.toString()); // 12-hour format with AM/PM
    }
  }

  /// Format a DateTime to a locale-aware date and time string
  static String formatDateTime(DateTime date, [Locale? locale]) {
    return '${formatDate(date, locale)} ${formatTime(date, locale)}';
  }

  /// Format a DateTime to a locale-aware date and time string with preferred time format
  static String formatDateTimeWithPreferredTime(DateTime date, [Locale? locale]) {
    return '${formatDate(date, locale)} ${getPreferredTimeFormat(locale).format(date)}';
  }

  /// Format a relative date (e.g., "Today", "Yesterday", "2 days ago")
  static String formatRelativeDate(DateTime date, [Locale? locale]) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final dateOnly = DateTime(date.year, date.month, date.day);
    final difference = today.difference(dateOnly).inDays;

    // For non-English locales, we could add translations here
    // For now, keeping English terms as they are widely understood
    if (difference == 0) {
      return 'Today';
    } else if (difference == 1) {
      return 'Yesterday';
    } else if (difference == -1) {
      return 'Tomorrow';
    } else if (difference > 1 && difference <= 7) {
      return '$difference days ago';
    } else if (difference < -1 && difference >= -7) {
      return 'In ${-difference} days';
    } else {
      return formatDate(date, locale);
    }
  }

  /// Format a relative date with locale-aware fallback
  static String formatRelativeDateWithContext(DateTime date, BuildContext context) {
    final locale = getCurrentLocale(context);
    return formatRelativeDate(date, locale);
  }

  /// Format a date for compact display (e.g., "3/15" for current year, "3/15/23" for other years)
  static String formatCompactDate(DateTime date, [Locale? locale]) {
    final now = DateTime.now();
    if (date.year == now.year) {
      // Same year, show month/day only
      return DateFormat.Md(locale?.toString()).format(date);
    } else {
      // Different year, show short date
      return formatShortDate(date, locale);
    }
  }

  /// Format a time duration in a human-readable way
  static String formatDuration(Duration duration, [Locale? locale]) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    final seconds = duration.inSeconds % 60;

    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else if (minutes > 0) {
      return '${minutes}m';
    } else {
      return '${seconds}s';
    }
  }

  /// Format a time duration with full precision
  static String formatDurationFull(Duration duration, [Locale? locale]) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    final seconds = duration.inSeconds % 60;

    if (hours > 0) {
      return '${hours}h ${minutes}m ${seconds}s';
    } else if (minutes > 0) {
      return '${minutes}m ${seconds}s';
    } else {
      return '${seconds}s';
    }
  }

  /// Get a locale-aware ordinal date format (e.g., "March 15th, 2024")
  static String formatOrdinalDate(DateTime date, [Locale? locale]) {
    final day = date.day;
    String ordinal;

    if (day >= 11 && day <= 13) {
      ordinal = '${day}th';
    } else {
      switch (day % 10) {
        case 1:
          ordinal = '${day}st';
          break;
        case 2:
          ordinal = '${day}nd';
          break;
        case 3:
          ordinal = '${day}rd';
          break;
        default:
          ordinal = '${day}th';
      }
    }

    // For non-English locales, fall back to regular date format
    if (locale != null && locale.languageCode != 'en') {
      return formatDate(date, locale);
    }

    final monthYear = DateFormat.yMMM(locale?.toString()).format(date);
    return '$monthYear $ordinal';
  }

  /// Format a date range with smart formatting based on the range span
  static String formatSmartDateRange(DateTime start, DateTime end, [Locale? locale]) {
    final daysDiff = end.difference(start).inDays;

    if (daysDiff == 0) {
      // Same day
      return formatDate(start, locale);
    } else if (daysDiff <= 7) {
      // Within a week, show both dates
      return formatDateRange(start, end, locale);
    } else if (daysDiff <= 31) {
      // Within a month, show compact range
      if (start.month == end.month && start.year == end.year) {
        // Use the same smart range formatting as formatDateRange
        final dateFormat = getDateFormat(locale);
        final testDate = DateTime(2024, 3, 15); // March 15, 2024
        final testFormatted = dateFormat.format(testDate);
        final dayFirst = testFormatted.startsWith('15') || testFormatted.startsWith('15/') || testFormatted.startsWith('15 ');

        if (dayFirst) {
          // Day-first format: "15-20 Mar 2024"
          final monthYear = getMonthFormat(locale).format(start);
          return '${start.day}-${end.day} $monthYear ${start.year}';
        } else {
          // Month-first format: "Mar 15-20, 2024"
          final monthFormat = getMonthFormat(locale);
          return '${monthFormat.format(start)} ${start.day}-${end.day}, ${start.year}';
        }
      } else {
        return formatDateRange(start, end, locale);
      }
    } else {
      // Longer range, show month-year format
      return '${formatMonthYear(start, locale)} - ${formatMonthYear(end, locale)}';
    }
  }

  /// Shorten four-digit years to two digits in a date string for compact display
  /// Example: "Mar 15, 2025" becomes "Mar 15, 25"
  static String shortenYearsInText(String text) {
    return text.replaceAllMapped(RegExp(r'(\d{4})(?!\d)'), (match) {
      final year = match.group(0)!;
      return year.substring(2);
    });
  }
}