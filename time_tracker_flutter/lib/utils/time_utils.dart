import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:time_tracker_flutter/models/time_entry.dart';
import 'package:time_tracker_flutter/utils/locale_date_utils.dart';

// Legacy format constants for backward compatibility - these now use system locale
DateFormat get dateFormat => LocaleDateUtils.getDateFormat();
DateFormat get weekDayFormat => LocaleDateUtils.getWeekdayFormat();
DateFormat get timeFormat => LocaleDateUtils.getTimeFormat();

// Context-aware formatting functions (recommended for new code)
String formatDateWithContext(DateTime dateTime, BuildContext context) {
  final locale = LocaleDateUtils.getCurrentLocale(context);
  return LocaleDateUtils.formatDate(dateTime, locale);
}

// Context-aware formatting with custom date format support (async)
Future<String> formatDateWithContextAsync(DateTime dateTime, BuildContext context) async {
  final locale = LocaleDateUtils.getCurrentLocale(context);
  return LocaleDateUtils.formatDateAsync(dateTime, locale);
}

// Context-aware formatting with custom date format support
Future<String> formatDateWithCustomAndContext(DateTime dateTime, BuildContext context) async {
  return LocaleDateUtils.formatDateWithCustomAndContext(dateTime, context);
}

String formatWeekdayWithContext(DateTime dateTime, BuildContext context) {
  debugPrint('formatWeekdayWithContext');
  final locale = LocaleDateUtils.getCurrentLocale(context);
  debugPrint('dateTime: $dateTime');
  debugPrint('locale: $locale');
  return LocaleDateUtils.formatWeekday(dateTime, locale);
}

String formatTimeWithContext(DateTime dateTime, BuildContext context) {
  final locale = LocaleDateUtils.getCurrentLocale(context);
  return LocaleDateUtils.formatTime(dateTime, locale);
}

// Function to format DateTime to locale-aware time format
String formatDateTime(DateTime dateTime, [Locale? locale]) {
  return LocaleDateUtils.getPreferredTimeFormat(locale).format(dateTime);
}

// Context-aware version of formatDateTime
String formatDateTimeWithContext(DateTime dateTime, BuildContext context) {
  final locale = LocaleDateUtils.getCurrentLocale(context);
  return formatDateTime(dateTime, locale);
}

// Function to round a DateTime object to the nearest specified minutes
String roundDateTime(String time, int? roundingMinutes) {
  final ttm = timeToMinutes(time);

  // Convert minutes to DateTime
  final dateTime = DateTime(0, 0, 0, ttm ~/ 60, ttm % 60);

  if (roundingMinutes == null || roundingMinutes <= 0) {
    // Always return in 24-hour HH:MM format for consistency
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  final int minutes = dateTime.minute;
  final int remainder = minutes % roundingMinutes;

  if (remainder == 0) {
    // Always return in 24-hour HH:MM format for consistency
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  DateTime roundedDateTime;
  // Round up if more than half of the rounding interval has passed
  if (remainder > roundingMinutes / 2) {
    roundedDateTime = dateTime.add(Duration(minutes: roundingMinutes - remainder));
  } else {
    // Round down
    roundedDateTime = dateTime.subtract(Duration(minutes: remainder));
  }

  // Always return in 24-hour HH:MM format for consistency
  return '${roundedDateTime.hour.toString().padLeft(2, '0')}:${roundedDateTime.minute.toString().padLeft(2, '0')}';
}

// Calculate total time from a list of time entries
String calculateTotalTime(List<TimeEntry> entries) {
  int totalMinutes = 0;

  for (final entry in entries) {
    if (entry.duration != null) {
      // If duration is directly provided
      totalMinutes += timeToMinutes(entry.duration!);
    } else if (entry.start != null && entry.end != null) {
      // If start and end times are provided
      totalMinutes += calculateMinutesBetween(entry.start!, entry.end!);
    }
  }

  return minutesToTime(totalMinutes);
}

// Convert time string (HH:MM) to minutes
int timeToMinutes(String time) {
  final parts = time.split(':');
  if (parts.length != 2) return 0;

  try {
    final hours = int.parse(parts[0]);
    final minutes = int.parse(parts[1]);
    return hours * 60 + minutes;
  } catch (e) {
    return 0;
  }
}

// Convert minutes to time string (HH:MM)
String minutesToTime(int minutes) {
  final hours = minutes ~/ 60;
  final mins = minutes % 60;
  return '${hours.toString().padLeft(2, '0')}:${mins.toString().padLeft(2, '0')}';
}

// Calculate minutes between two time strings (HH:MM)
int calculateMinutesBetween(String start, String end) {
  final startParts = start.split(':');
  final endParts = end.split(':');

  if (startParts.length != 2 || endParts.length != 2) return 0;

  try {
    final startHour = int.parse(startParts[0]);
    final startMinute = int.parse(startParts[1]);
    final endHour = int.parse(endParts[0]);
    final endMinute = int.parse(endParts[1]);

    final startTotalMinutes = startHour * 60 + startMinute;
    final endTotalMinutes = endHour * 60 + endMinute;

    // Handle cases where end time is on the next day
    if (endTotalMinutes < startTotalMinutes) {
      return (24 * 60 - startTotalMinutes) + endTotalMinutes;
    }

    return endTotalMinutes - startTotalMinutes;
  } catch (e) {
    return 0;
  }
}

// Get the start date of the week containing the given date
DateTime getWeekStartDate(DateTime date) {
  // Using Monday as the first day of the week
  final dayOfWeek = date.weekday;
  return date.subtract(Duration(days: dayOfWeek - 1));
}

// Get the end date of the week containing the given date
DateTime getWeekEndDate(DateTime date) {
  // Using Sunday as the last day of the week
  final dayOfWeek = date.weekday;
  return date.add(Duration(days: 7 - dayOfWeek));
}

// Get a key for the current week (e.g., "2023-01-02 - 2023-01-08")
String getCurrentWeekKey() {
  final now = DateTime.now();
  final weekStart = getWeekStartDate(now);
  final weekEnd = getWeekEndDate(now);
  return '${DateFormat('yyyy-MM-dd').format(weekStart)} - ${DateFormat('yyyy-MM-dd').format(weekEnd)}'; // Keep ISO format for data consistency
}

// Group time entries by week
Map<String, Map<String, dynamic>> groupEntriesByWeek(List<TimeEntry> entries) {
  final Map<String, List<TimeEntry>> weekGroups = {};

  for (final entry in entries) {
    final entryDate = DateTime.parse(entry.date);
    final weekStart = getWeekStartDate(entryDate);
    final weekEnd = getWeekEndDate(entryDate);
    final weekKey = '${DateFormat('yyyy-MM-dd').format(weekStart)} - ${DateFormat('yyyy-MM-dd').format(weekEnd)}'; // Keep ISO format for data consistency

    if (!weekGroups.containsKey(weekKey)) {
      weekGroups[weekKey] = [];
    }

    weekGroups[weekKey]!.add(entry);
  }

  // Calculate total time for each week
  final Map<String, Map<String, dynamic>> result = {};
  weekGroups.forEach((weekKey, weekEntries) {
    result[weekKey] = {
      'totalTime': calculateTotalTime(weekEntries),
      'entries': weekEntries,
    };
  });

  return result;
}

// Format a week key for display (uses default locale)
String formatWeekDisplay(String weekKey) {
  final parts = weekKey.split(' - ');
  if (parts.length != 2) return weekKey;

  try {
    final startDate = DateTime.parse(parts[0]);
    final endDate = DateTime.parse(parts[1]);
    return LocaleDateUtils.formatWeekDisplay(startDate, endDate);
  } catch (e) {
    return weekKey;
  }
}

// Context-aware version of formatWeekDisplay
String formatWeekDisplayWithContext(String weekKey, BuildContext context) {
  final parts = weekKey.split(' - ');
  if (parts.length != 2) return weekKey;

  try {
    final startDate = DateTime.parse(parts[0]);
    final endDate = DateTime.parse(parts[1]);
    final locale = LocaleDateUtils.getCurrentLocale(context);
    return LocaleDateUtils.formatWeekDisplay(startDate, endDate, locale);
  } catch (e) {
    return weekKey;
  }
}

// Check if a week key represents the current week
bool isCurrentWeek(String weekKey) {
  return weekKey == getCurrentWeekKey();
}

// Calculate hours from start and end time strings (HH:MM)
double calculateHours(String start, String end) {
  final minutes = calculateMinutesBetween(start, end);
  return minutes / 60.0;
}

// Parse duration string (HH:MM) to hours as double
double parseDurationToHours(String duration) {
  final minutes = timeToMinutes(duration);
  return minutes / 60.0;
}
